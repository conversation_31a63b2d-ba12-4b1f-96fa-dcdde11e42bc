import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Credito, CreditoCartao, Perfil, SituacaoFimLimiteCartao, UsuarioAutenticado } from "cpesc-shared";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import { IsNull, Raw, Repository } from "typeorm";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiLimiteCartao } from "./limite-cartao.entity.js";

@Injectable()
export class LimiteCartaoService {
  constructor(
    @InjectRepository(ApiLimiteCartao) private limiteCartaoRepository: Repository<ApiLimiteCartao>,
    private movimentacaoService: MovimentacaoService,
  ) {}

  async getLimiteCartoes(): Promise<ApiLimiteCartao[]> {
    return this.limiteCartaoRepository.find({
      where: {
        id: Raw(() => "ROWNUM <= 10"),
      },
      order: { dataCredito: "DESC", fimLimiteCartao: { id: "DESC" } },
    });
  }

  async getGastosPorCreditoId(usuario: UsuarioAutenticado, idCredito: number): Promise<Credito> {
    const limiteCartao: Credito | null = await this.limiteCartaoRepository.findOne({
      where: {
        id: idCredito,
        ...(usuario.perfil === Perfil.Portador && {
          cartao: {
            portadorUnidadeAdministrativa: {
              portador: { id: usuario.id },
            },
          },
        }),
      },
      relations: {
        fimLimiteCartao: true,
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: true,
          },
        },
      },
      order: { dataCredito: "ASC" },
    });

    if (limiteCartao == null) {
      throw new BadRequestException("Limite de crédito não encontrado para este portador.");
    }

    if (limiteCartao.fimLimiteCartao?.situacao === SituacaoFimLimiteCartao.Finalizado) {
      const limiteComMovimentacoes = await this.getGastosPorCreditoIdAssociados(idCredito);
      assertNotNull(limiteComMovimentacoes, "Limite com movimentações não encontrado.");
      limiteComMovimentacoes.temMaisLimites = false;
      return limiteComMovimentacoes;
    } else {
      const temMaisLimites = await this.temMaisLimites(
        limiteCartao.cartao.portadorUnidadeAdministrativa.portador.id,
        limiteCartao.dataInicioMovimentacao,
      );

      const limiteSemMovimentacoes = await this.limiteCartaoRepository.findOne({
        where: { id: idCredito },
        relations: {
          cartao: {
            portadorUnidadeAdministrativa: {
              portador: true,
              unidadeAdministrativa: { municipio: true, unidadeGestora: { contaBanco: true } },
            },
          },
          subelemento: true,
          fimLimiteCartao: true,
          movimentacoes: {
            documentoFiscal: true,
          },
        },
      });

      if (limiteSemMovimentacoes !== null) {
        const movimentacoes = await this.movimentacaoService.getMovimentacoesPorPeriodo(
          idCredito,
          limiteSemMovimentacoes.dataLimiteMovimentacao,
          limiteSemMovimentacoes.cartao.nuContaCartao,
        );
        (limiteSemMovimentacoes as CreditoCartao).temMaisLimites = temMaisLimites;
        limiteSemMovimentacoes.cartao.movimentacoes = movimentacoes;
        return limiteSemMovimentacoes;
      } else {
        return limiteCartao;
      }
    }
  }

  async getGastosPorCreditoIdAssociados(idCredito: number): Promise<Credito | null> {
    const gastos = await this.limiteCartaoRepository.findOne({
      where: {
        id: idCredito,
      },
      relations: {
        movimentacoes: {
          documentoFiscal: { documentoFiscalItens: true },
        },
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: true,
            unidadeAdministrativa: { municipio: true, unidadeGestora: { contaBanco: true } },
          },
        },
        subelemento: true,
        fimLimiteCartao: true,
      },
      order: {
        movimentacoes: {
          dataTransacao: "ASC",
          valorTransacaoReal: "DESC",
        },
      },
    });

    if (gastos?.movimentacoes) {
      gastos.movimentacoes = gastos.movimentacoes.map(mov => ({
        ...mov,
        nomeEstabelecimento: mov.nomeEstabelecimento
          ? mov.nomeEstabelecimento.length > 1
            ? mov.nomeEstabelecimento
            : mov.descricaoTransacao
          : mov.descricaoTransacao,
        CNPJEstabelecimento: mov.CNPJEstabelecimento === "00000000000000" ? "" : mov.CNPJEstabelecimento,
        valorTransacaoReal: mov.codigoTransacaoBB === 253600 ? mov.valorTransacaoReal * -1 : mov.valorTransacaoReal,
      }));
    }

    return gastos;
  }

  async temMaisLimites(idPortador: number, dataInicial: Date): Promise<boolean> {
    if (!idPortador) return false;

    const limites = await this.limiteCartaoRepository.find({
      where: {
        dataInicioMovimentacao: dataInicial,
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: { id: idPortador },
          },
        },
        fimLimiteCartao: [{ limiteCartaoId: IsNull() }, { situacao: SituacaoFimLimiteCartao.Edicao }], // Considera limites sem finalização ou em edição
      },
    });

    if (limites.length > 1) return true;

    return false;
  }

  async getLimitesPorPortadorId(id: number): Promise<ApiLimiteCartao[]> {
    return this.limiteCartaoRepository.find({
      where: {
        cartao: { portadorUnidadeAdministrativa: { portador: { id } } },
      },
      relations: {
        subelemento: true,
        fimLimiteCartao: true,
        cartao: {
          portadorUnidadeAdministrativa: {
            unidadeAdministrativa: { municipio: true, unidadeGestora: true },
            portador: true,
          },
        },
      },
      order: { dataCredito: "DESC", fimLimiteCartao: { id: "DESC" } },
    });
  }

  async getLimitesPorPortadorUa(idPortador: number, idUa: number): Promise<ApiLimiteCartao[]> {
    const limites = await this.limiteCartaoRepository.find({
      where: {
        cartao: {
          portadorUnidadeAdministrativa: {
            portador: { id: idPortador },
            unidadeAdministrativa: { id: idUa },
          },
        },
      },
      relations: {
        subelemento: true,
        fimLimiteCartao: true,
        cartao: { portadorUnidadeAdministrativa: { unidadeAdministrativa: { municipio: true, unidadeGestora: true } } },
      },
      order: { dataCredito: "DESC", fimLimiteCartao: { id: "DESC", situacao: "DESC" } },
    });
    return limites;
  }

  async updateGastosCartao(idCredito: number, updateLimiteCartao: Partial<ApiLimiteCartao>): Promise<ApiLimiteCartao> {
    const limiteExistente = await this.limiteCartaoRepository.findOne({
      where: { id: idCredito },
      relations: { fimLimiteCartao: true, cartao: { movimentacoes: true } },
    });
    assertNotNull(limiteExistente, "Limite de cartão não encontrado.");
    const limite = this.limiteCartaoRepository.merge(limiteExistente, updateLimiteCartao);

    return this.limiteCartaoRepository.save(limite);
  }

  async finalizarLimiteCartao(id: number): Promise<void> {
    const limite = await this.limiteCartaoRepository.findOne({ where: { id }, relations: { fimLimiteCartao: true } });
    const dataAtual = new Date();
    if (!limite) {
      throw new Error("Limite de cartão não encontrado.");
    }
    if (limite.fimLimiteCartao?.situacao === SituacaoFimLimiteCartao.Finalizado) {
      if (dataAtual.setHours(0, 0, 0, 0) <= limite.dataVencimento.setHours(0, 0, 0, 0))
        limite.dataLimiteMovimentacao = dataAtual;

      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      limite.dataZeramento ??= dataAtual;
    } else {
      if (limite.fimLimiteCartao?.situacao === SituacaoFimLimiteCartao.Edicao)
        limite.dataLimiteMovimentacao = limite.dataVencimento;
    }
    await this.limiteCartaoRepository.save(limite);
  }
}
