import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { DocumentoFiscalSigef, TipoDocumentoFiscal } from "cpesc-shared";
import type {
  PayloadCriarDocFiscalImportacao,
  PayloadCriarDocFiscalManual,
  PayloadCriarReversao,
} from "cpesc-shared/out/endpoints/documento-fiscal.js";
import { PayloadCriarDocFiscalItem } from "cpesc-shared/src/endpoints/documento-fiscal-item.js";
import { DataSource, FindOptionsWhere, Repository, UpdateResult } from "typeorm";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiDocumentoFiscal } from "./documento-fiscal.entity.js";

@Injectable()
export class DocumentoFiscalService {
  constructor(
    @InjectRepository(ApiDocumentoFiscal) private documentoFiscalRepository: Repository<ApiDocumentoFiscal>,
    private docfiscalitemService: DocumentoFiscalItemService,
    private dataSource: DataSource,
  ) {}

  async criarDocumentoFiscalManualComItens(
    dados: PayloadCriarDocFiscalManual,
    dataEmissao: Date,
    emailCriador: string,
  ): Promise<ApiDocumentoFiscal | null> {
    try {
      const documentoFiscal: Omit<
        ApiDocumentoFiscal,
        "id" | "numeroSerie" | "documentoFiscalItens" | "movimentacao" | "generateFields"
      > = {
        movimentacaoId: dados.movimentacaoId,
        tipodocumentofiscalId: dados.tipodocumentofiscalId,
        numero: dados.numero,
        serie: dados.serie,
        cnpj: dados.cnpj,
        chave: null,
        valor: dados.valor,
        codigoSigef: null,
        desconto: dados.desconto ?? null,
        acrescimo: dados.acrescimo ?? null,
        dataEmissao,
        criadoPor: emailCriador,
        criadoEm: new Date(),
        atualizadoPor: emailCriador,
        atualizadoEm: new Date(),
      };
      const nf = await this.documentoFiscalRepository.save(documentoFiscal);
      const itens = dados.documentoFiscalItens;
      if (itens) {
        await this.docfiscalitemService.criarItemManual(itens as PayloadCriarDocFiscalItem[], nf.id, emailCriador);
      }

      return nf;
    } catch (error) {
      console.log("Erro ao salvar o documento fiscal", error);
      return null;
    }
  }

  async vincularDocumentoFiscalImportacao(
    dados: PayloadCriarDocFiscalImportacao,
    emailCriador: string,
  ): Promise<ApiDocumentoFiscal | null> {
    try {
      if (!dados.chave) {
        console.log("Nota fiscal sem chave.");
        return null;
      }

      const listaNotasSigef = await this.getDocumentoFiscalNoSIGEF(dados.chave);

      if (listaNotasSigef.length === 0) {
        console.log("Nota fiscal não encontrada com a chave:", dados.chave);
        return null;
      }

      const notaSigef = listaNotasSigef[0];
      const documentoFiscal: Omit<
        ApiDocumentoFiscal,
        | "id"
        | "numeroSerie"
        | "documentoFiscalItens"
        | "desconto"
        | "acrescimo"
        | "movimentacao"
        | "generateFields"
        | "atualizadoEm"
        | "criadoEm"
      > = {
        movimentacaoId: dados.movimentacaoId,
        tipodocumentofiscalId: dados.tipodocumentofiscalId,
        numero: notaSigef.numero,
        serie: notaSigef.serie,
        cnpj: notaSigef.cnpj,
        chave: notaSigef.chave,
        valor: notaSigef.valor,
        dataEmissao: notaSigef.dataEmissao,
        codigoSigef: notaSigef.codigoSigef,
        criadoPor: emailCriador,
        atualizadoPor: emailCriador,
      };

      const nf = await this.documentoFiscalRepository.save(documentoFiscal);
      await this.docfiscalitemService.criarItemBuscandoSigef(dados.codigoSigef, nf.id, emailCriador);

      return nf;
    } catch (error) {
      console.log("Erro ao buscar o documento fiscal por chave", error);
      return null;
    }
  }

  async atualizarDocumentoFiscal(
    id: number,
    emailUsuario: string,
    documentoFiscal: Partial<ApiDocumentoFiscal>,
  ): Promise<UpdateResult | undefined> {
    try {
      documentoFiscal.atualizadoPor = emailUsuario;
      const { documentoFiscalItens, ...documentoFiscalSemItens } = documentoFiscal;
      const nfAtualizada = await this.documentoFiscalRepository.update(id, documentoFiscalSemItens);
      await this.docfiscalitemService.atualizarItem(documentoFiscal.documentoFiscalItens ?? [], id, emailUsuario);

      return nfAtualizada;
    } catch (error) {
      console.log("Erro ao atualizar o documento fiscal", error);
      return undefined;
    }
  }

  async getPorId(id: number): Promise<ApiDocumentoFiscal | null> {
    return this.documentoFiscalRepository.findOneBy({ id });
  }

  async findAll(params: FindOptionsWhere<ApiDocumentoFiscal>): Promise<ApiDocumentoFiscal[]> {
    return this.documentoFiscalRepository.find({
      where: params,
      relations: { documentoFiscalItens: true },
    });
  }

  async removeDocumentoFiscalEDocumentoFiscalItem(idRemover: number) {
    await this.docfiscalitemService.removeByDocumentoFiscalId(idRemover);
    return this.documentoFiscalRepository.delete({ id: idRemover });
  }

  async getDocumentoFiscalNoSIGEF(parametro: string): Promise<DocumentoFiscalSigef[]> {
    let query: string;
    let params: string[];

    const baseQuery = `SELECT
          S.NUDOCNFE AS NUMERO,
          S.NUSERIENFE AS SERIE,
          S.NUDOCNFE || '-'|| S.NUSERIENFE AS NUMEROSERIE,
          S.NUCNPJEMITENTE AS CNPJ,
          S.NUCHAVEACESSO AS CHAVE,
          S.VLTOTALNOTAFISCAL AS VALOR,
          S.DTEMISSAO AS DATAEMISSAO,
          S.NUNFEID AS CODIGOSIGEF
         FROM SIGEF.EFINNOTAFISCALSAT@SIGEFCPESC S`;

    if (parametro.includes("-")) {
      const listaparametros = parametro.split("-");
      query = `${baseQuery} WHERE S.NUCNPJEMITENTE = :p1 AND S.NUDOCNFE = :p2 AND S.NUSERIENFE = :p3`;
      params = listaparametros;
    } else {
      query = `${baseQuery} WHERE S.NUCHAVEACESSO = :pchave`;
      params = [parametro];
    }

    const resultado = await this.dataSource.query<
      {
        NUMERO: number;
        SERIE: number;
        NUMEROSERIE: string;
        CNPJ: string;
        CHAVE: string;
        VALOR: number;
        DATAEMISSAO: string;
        CODIGOSIGEF: number;
      }[]
    >(query, params);

    return resultado.map(element => ({
      numero: element.NUMERO,
      serie: element.SERIE,
      numeroSerie: element.NUMEROSERIE,
      cnpj: element.CNPJ,
      chave: element.CHAVE,
      valor: element.VALOR,
      dataEmissao: new Date(element.DATAEMISSAO),
      codigoSigef: element.CODIGOSIGEF,
    }));
  }

  //--Reversoes
  async getReversao(movimentacaoId: number): Promise<ApiDocumentoFiscal | null> {
    try {
      return await this.documentoFiscalRepository.findOneByOrFail({
        movimentacaoId,
        tipodocumentofiscalId: TipoDocumentoFiscal.reversao,
      });
    } catch (error) {
      console.log("Erro ao buscar a reversão", error);
      return null;
    }
  }

  async salvarReversao(
    reversao: PayloadCriarReversao,
    emailCriador: string,
    dataAtual: Date,
  ): Promise<ApiDocumentoFiscal> {
    const reversaoMovimentacao: Omit<
      ApiDocumentoFiscal,
      | "id"
      | "numeroSerie"
      | "documentoFiscalItens"
      | "movimentacao"
      | "generateFields"
      | "serie"
      | "cnpj"
      | "chave"
      | "valor"
      | "codigoSigef"
      | "desconto"
      | "acrescimo"
      | "atualizadoEm"
      | "atualizadoPor"
    > = {
      movimentacaoId: reversao.movimentacaoId,
      tipodocumentofiscalId: TipoDocumentoFiscal.reversao,
      numero: reversao.numero,
      dataEmissao: reversao.dataEmissao,
      criadoEm: dataAtual,
      criadoPor: emailCriador,
    };

    try {
      const reversaoNova = this.documentoFiscalRepository.create(reversaoMovimentacao);
      await this.documentoFiscalRepository.save(reversaoNova);
      return reversaoNova;
    } catch (error) {
      console.log("Erro ao salvar a reversão", error);
      throw new BadRequestException("Ocorreu um erro ao tentar vincular a Reversão.");
    }
  }

  async removeReversao(id: number) {
    return this.documentoFiscalRepository.delete({ id, tipodocumentofiscalId: TipoDocumentoFiscal.reversao });
  }
}
